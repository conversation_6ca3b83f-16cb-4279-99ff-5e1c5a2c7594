{"$schema": "http://json-schema.org/draft-07/schema#", "title": "APIX Protocol v1.0.0", "description": "WebSocket-based communication protocol for Universal AI UI", "version": "1.0.0", "definitions": {"BaseEvent": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique event identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp"}, "conversationId": {"type": "string", "description": "Conversation identifier"}, "userId": {"type": "string", "description": "User identifier"}}, "required": ["id", "timestamp", "conversationId", "userId"]}, "UserMessage": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "user_message"}, "data": {"type": "object", "properties": {"content": {"type": "string", "description": "User message content"}, "agentId": {"type": "string", "description": "Target agent identifier"}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"type": {"enum": ["image", "file", "audio"]}, "url": {"type": "string"}, "name": {"type": "string"}, "size": {"type": "number"}}}}}, "required": ["content", "agentId"]}}}]}, "TextChunk": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "text_chunk"}, "data": {"type": "object", "properties": {"content": {"type": "string", "description": "Partial response content"}, "isComplete": {"type": "boolean", "description": "Whether this is the final chunk"}, "messageId": {"type": "string", "description": "Associated message identifier"}}, "required": ["content", "isComplete", "messageId"]}}}]}, "ThinkingStatus": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "thinking_status"}, "data": {"type": "object", "properties": {"status": {"enum": ["thinking", "processing", "generating", "complete"], "description": "Current processing status"}, "message": {"type": "string", "description": "Status description"}, "progress": {"type": "number", "minimum": 0, "maximum": 100, "description": "Progress percentage"}}, "required": ["status"]}}}]}, "ToolCallStart": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "tool_call_start"}, "data": {"type": "object", "properties": {"toolId": {"type": "string", "description": "Tool identifier"}, "toolName": {"type": "string", "description": "Tool name"}, "input": {"type": "object", "description": "Tool input parameters"}, "callId": {"type": "string", "description": "Unique call identifier"}}, "required": ["toolId", "toolName", "input", "callId"]}}}]}, "ToolCallResult": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "tool_call_result"}, "data": {"type": "object", "properties": {"callId": {"type": "string", "description": "Associated call identifier"}, "result": {"description": "Tool execution result"}, "success": {"type": "boolean", "description": "Whether execution was successful"}, "error": {"type": "string", "description": "Error message if failed"}, "executionTime": {"type": "number", "description": "Execution time in milliseconds"}}, "required": ["callId", "success"]}}}]}, "Error": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "error"}, "data": {"type": "object", "properties": {"code": {"type": "string", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}, "details": {"type": "object", "description": "Additional error details"}, "recoverable": {"type": "boolean", "description": "Whether error is recoverable"}}, "required": ["code", "message"]}}}]}, "StateUpdate": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "state_update"}, "data": {"type": "object", "properties": {"state": {"type": "object", "description": "Updated state data"}, "path": {"type": "string", "description": "State path that changed"}, "operation": {"enum": ["create", "update", "delete"], "description": "Type of state change"}}, "required": ["state", "operation"]}}}]}, "RequestUserInput": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "request_user_input"}, "data": {"type": "object", "properties": {"requestId": {"type": "string", "description": "Unique request identifier"}, "prompt": {"type": "string", "description": "Prompt for user input"}, "schema": {"type": "object", "description": "JSON schema for expected input"}, "timeout": {"type": "number", "description": "Timeout in milliseconds"}}, "required": ["requestId", "prompt", "schema"]}}}]}, "UserResponse": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "user_response"}, "data": {"type": "object", "properties": {"requestId": {"type": "string", "description": "Associated request identifier"}, "response": {"description": "User response data"}, "cancelled": {"type": "boolean", "description": "Whether request was cancelled"}}, "required": ["requestId"]}}}]}, "ControlSignal": {"allOf": [{"$ref": "#/definitions/BaseEvent"}, {"type": "object", "properties": {"type": {"const": "control_signal"}, "data": {"type": "object", "properties": {"signal": {"enum": ["pause", "resume", "cancel", "restart"], "description": "Control signal type"}, "target": {"type": "string", "description": "Target identifier (conversation, tool call, etc.)"}, "reason": {"type": "string", "description": "Reason for signal"}}, "required": ["signal"]}}}]}}, "oneOf": [{"$ref": "#/definitions/UserMessage"}, {"$ref": "#/definitions/TextChunk"}, {"$ref": "#/definitions/ThinkingStatus"}, {"$ref": "#/definitions/ToolCallStart"}, {"$ref": "#/definitions/ToolCallResult"}, {"$ref": "#/definitions/Error"}, {"$ref": "#/definitions/StateUpdate"}, {"$ref": "#/definitions/RequestUserInput"}, {"$ref": "#/definitions/UserResponse"}, {"$ref": "#/definitions/ControlSignal"}]}