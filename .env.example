# Database Configuration
DATABASE_URL=postgresql://uaui_user:uaui_password@localhost:5432/uaui_db
POSTGRES_DB=uaui_db
POSTGRES_USER=uaui_user
POSTGRES_PASSWORD=uaui_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Server Configuration
NODE_ENV=development
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-change-in-production
ENCRYPTION_KEY=your-32-character-encryption-key-here

# AI Provider API Keys (Required for production)
OPENAI_API_KEY=sk-your-openai-api-key-here
CLAUDE_API_KEY=your-claude-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
MISTRAL_API_KEY=your-mistral-api-key-here
GROQ_API_KEY=your-groq-api-key-here

# Frontend URLs
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AI_CALLS_PER_MINUTE=20

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3003
GRAFANA_ADMIN_PASSWORD=admin

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:3002
SESSION_SECRET=your-session-secret-here

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Cache Settings
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000
