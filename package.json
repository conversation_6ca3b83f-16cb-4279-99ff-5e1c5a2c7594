{"name": "uaui-apix-app", "version": "1.0.0", "description": "Universal AI UI with APIX Protocol - Production-ready multi-provider AI chat system", "private": true, "workspaces": ["backend", "frontend", "admin-panel", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "dev:admin": "npm run dev --workspace=admin-panel", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "test:e2e": "npm run test:e2e --workspace=frontend", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "npm run clean --workspaces && rm -rf node_modules"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/uaui-apix-app.git"}, "keywords": ["ai", "chat", "websocket", "openai", "claude", "gemini", "mistral", "groq", "apix", "uaui"], "author": "UAUI Team", "license": "MIT"}