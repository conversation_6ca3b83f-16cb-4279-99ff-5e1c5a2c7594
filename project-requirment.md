### 🧠 AI Build Prompt for UAUI + APIX Production System

**Objective:** Build a fully functional, real-world, production-ready Universal AI UI (UAUI) application that uses the APIX Protocol for WebSocket-based interaction, routes user queries across multiple AI providers, and includes an admin panel for complete platform control.

---

### ✅ Key Requirements (All Features Are Mandatory)

**Must Be Real Code — No Mocks, No Stubs, No Placeholder Logic**

* All AI providers (OpenAI, Claude, Gemini, Mistral, Groq) must use real API calls.
* All WebSocket events must be triggered, emitted, and handled with state management.
* Real-time updates must reflect immediately in the chat UI and Redux state.
* Admin panel must allow management of API keys, providers, and clients.
* Caching, rate limiting, HITL, and state sync must be implemented in Redis/Postgres.
* All services must use real configurations via `.env` and Docker Compose.

---

### 🧰 Technologies to Use

**Backend:**

* TypeScript (Node.js)
* Fastify (Web server)
* Socket.IO (WebSocket)
* Redis (state, cache, rate-limit)
* PostgreSQL (data store)
* Winston (logging)
* RxJS (advanced event system)

**Frontend:**

* React + Vite + TypeScript
* Tailwind CSS
* Socket.IO Client
* Redux Toolkit

**DevOps:**

* Docker + docker-compose
* GitHub Actions (CI/CD)
* Prometheus + Grafana (monitoring)

---

### 🔌 APIX Protocol (v1.0.0)

Use event-driven WebSocket-based communication. The following events must be implemented:

* `user_message`
* `text_chunk`
* `thinking_status`
* `tool_call_start`
* `tool_call_result`
* `error`
* `state_update`
* `request_user_input`
* `user_response`
* `control_signal`

---

### 🔄 Functional Modules to Build

**Backend Core:**

* `UAUICoreEngine.ts`
* `AIProviderAdapters/` (one file for each: OpenAI, Claude, Gemini, etc.)
* `SmartProviderSelector.ts`
* `StateManager.ts` (Redis)
* `EventBus.ts` (Node + RxJS)
* `Logger.ts` (Winston)
* `RateLimiter.ts`
* `Auth.ts` (JWT middleware)
* REST APIs for admin panel

**Frontend:**

* `ChatUI.tsx` – real-time UI with all APIX events
* `SocketClient.ts` – manages all WebSocket traffic
* `ReduxStore.ts` – state management per conversation/user
* HITL UI: dynamically rendered forms based on `request_user_input`

**Admin Panel (Separate App):**

* Provider management (API keys, routing rules)
* Client app & API key management
* HITL agent configuration
* Rate limit settings & logs
* System monitoring dashboards

---

### 🔒 Security

* Use `.env` for secrets and load securely
* API keys encrypted (AES-256) in PostgreSQL
* Admin routes protected by JWT
* Input/output sanitization on all API calls
* Rate limiting (Redis-based) for abuse prevention

---


---

### 🧪 Testing (Required for All Modules)

* **Jest:** backend logic (95%+ coverage)
* **Cypress:** frontend + WebSocket flow
* **Stress Test:** 500 concurrent WebSocket clients
* **Security Tests:** token auth, rate limit, XSS, replay attack protection

---

### 🧭 Build Strategy

1. Setup monorepo + Docker Compose stack
2. Build Fastify backend with Redis/Postgres integration
3. Implement OpenAI provider first, with real API key (Phase 1)
4. Integrate APIX event mapping via WebSocket
5. Develop React Chat UI with event feedback loop
6. Add Claude, Gemini, Mistral, Groq with fallback support
7. Build Admin Panel (REST + React)
8. Add HITL and SmartProvider routing logic
9. Optimize performance, caching, and monitoring
10. Deploy to cloud and document everything

---

### 🚫 Forbidden

* No placeholder UI
* No mock responses
* No hardcoded demo tokens
* No untyped code
* No incomplete endpoint stubs


**Instruction to AI Developer:** Build every file and logic block as if you’re launching this to enterprise clients. Every module must be functional, integrated, secure, and testable.
