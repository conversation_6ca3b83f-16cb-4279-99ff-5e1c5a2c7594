{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@shared/*": ["shared/src/*"], "@backend/*": ["backend/src/*"], "@frontend/*": ["frontend/src/*"], "@admin/*": ["admin-panel/src/*"]}}, "include": ["backend/src/**/*", "frontend/src/**/*", "admin-panel/src/**/*", "shared/src/**/*", "**/*.d.ts"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.test.ts", "**/*.spec.ts"]}