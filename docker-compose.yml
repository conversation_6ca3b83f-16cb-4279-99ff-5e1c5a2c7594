version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: uaui-postgres
    environment:
      POSTGRES_DB: uaui_db
      POSTGRES_USER: uaui_user
      POSTGRES_PASSWORD: uaui_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - uaui-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U uaui_user -d uaui_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache & State Store
  redis:
    image: redis:7-alpine
    container_name: uaui-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - uaui-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: uaui-backend
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: **************************************************/uaui_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      CLAUDE_API_KEY: ${CLAUDE_API_KEY}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      MISTRAL_API_KEY: ${MISTRAL_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - uaui-network
    restart: unless-stopped

  # Frontend Chat UI
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: uaui-frontend
    ports:
      - "3000:3000"
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_WS_URL: ws://localhost:3001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - uaui-network
    restart: unless-stopped

  # Admin Panel
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: uaui-admin
    ports:
      - "3002:3002"
    environment:
      VITE_API_URL: http://localhost:3001
      VITE_WS_URL: ws://localhost:3001
    volumes:
      - ./admin-panel:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - uaui-network
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: uaui-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - uaui-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: uaui-grafana
    ports:
      - "3003:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - uaui-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  uaui-network:
    driver: bridge
